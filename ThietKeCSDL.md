# 📊 Cấu Tr<PERSON> Sở Dữ Liệu

## 1. Bảng: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Đơn Vị H<PERSON>)

**Mụ<PERSON> đích**: <PERSON>u<PERSON>n lý thông tin hành chính theo cấp tỉnh, thành liên quan đến đề tài.

| STT | Tên <PERSON>r<PERSON>ờ<PERSON>    | Kiểu <PERSON> Li<PERSON>/Thu<PERSON>                | <PERSON>hi <PERSON>                     |
|-----|---------------|----------------------------------------|-----------------------------|
| 1   | MaDonViHC     | INT, AUTO_INCREMENT, Khóa chính        | Định danh duy nhất          |
| 2   | TinhThanh     | VARCHAR (NOT NULL)                    | Tên tỉnh, thành             |

---

## 2. Bảng: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Đơn V<PERSON>)

**M<PERSON>c đích**: <PERSON><PERSON><PERSON> trữ thông tin các đơn vị phối hợp thực hiện đề tài.

| STT | Tên Trường    | Kiểu Dữ Liệu/Thuộc Tính                | Ghi Chú                     |
|-----|---------------|----------------------------------------|-----------------------------|
| 1   | MaDonVi       | INT, AUTO_INCREMENT, Khóa chính        | Mã đơn vị                   |
| 2   | TenDonVi      | VARCHAR                               | Tên đơn vị                  |
| 3   | DiaChi        | VARCHAR                               | Địa chỉ                     |
| 4   | SoDienThoai   | VARCHAR                               | Số điện thoại               |
| 5   | Email         | VARCHAR                               | Email                       |

---

## 2.1. Bảng: DeTai_DonVi (Quan Hệ Đề Tài - Đơn Vị)

**Mục đích**: Liên kết đề tài với các đơn vị có liên quan (chủ trì, phối hợp, …).

| STT | Tên Trường    | Kiểu Dữ Liệu/Thuộc Tính                | Ghi Chú                     |
|-----|---------------|----------------------------------------|-----------------------------|
| 1   | MaDeTai       | INT, Khóa ngoại (tham chiếu bảng DeTai) |                             |
| 2   | MaDonVi       | INT, Khóa ngoại (tham chiếu bảng DonViPhoiHop) |                     |
| 3   | GhiChu        | TEXT                                   | Ghi chú                     |

**Khóa chính**: Kết hợp `MaDeTai` + `MaDonVi`.

---

## 3. Bảng: DeTai (Đề Tài)

**Mục đích**: Lưu trữ thông tin chung về đề tài.

| STT | Tên Trường        | Kiểu Dữ Liệu/Thuộc Tính                | Ghi Chú                     |
|-----|-------------------|----------------------------------------|-----------------------------|
| 1   | MaDeTai           | INT, AUTO_INCREMENT, Khóa chính        | Mã đề tài                   |
| 2   | TenDeTai          | TEXT                                   | Tên đề tài                  |
| 3   | MoTaTomTat        | TEXT                                   | Mô tả tóm tắt đề tài        |
| 4   | LinhVuc           | VARCHAR                               | Lĩnh vực của đề tài         |
| 5   | ThoiGianBatDau    | DATE                                  | Ngày bắt đầu đề tài         |
| 6   | ThoiGianKetThuc   | DATE                                  | Ngày kết thúc đề tài        |
| 7   | CapQuanLy         | ENUM('Nhà nước', 'Bộ', 'Ngành', 'Cơ sở') | Cấp quản lý đề tài, mặc định chọn: Nhà nước, Bộ, Ngành, Cơ sở |

---

## 4. Bảng: CanBo (Cán Bộ)

**Mục đích**: Lưu trữ thông tin cá nhân của cán bộ.

| STT | Tên Trường        | Kiểu Dữ Liệu/Thuộc Tính                | Ghi Chú                     |
|-----|-------------------|----------------------------------------|-----------------------------|
| 1   | MaCanBo           | INT, AUTO_INCREMENT, Khóa chính        | Mã cán bộ                   |
| 2   | HoTen             | VARCHAR                               | Họ tên                      |
| 3   | ChucVu            | VARCHAR                               | Chức vụ                     |
| 4   | QuanHam           | VARCHAR                               | Quân hàm                    |
| 5   | NgaySinh          | DATE                                  | Ngày tháng năm sinh         |
| 6   | GioiTinh          | ENUM('Nam', 'Nữ')                     | Giới tính, mặc định chọn: Nam hoặc Nữ |
| 7   | HocVi             | VARCHAR                               | Học vị                      |
| 8   | Nam_HocVi         | YEAR                                  | Năm nhận học vị             |
| 9   | HocHam            | VARCHAR                               | Học hàm                     |
| 10  | Nam_HocHam        | YEAR                                  | Năm nhận học hàm            |
| 11  | ChucDanhCMKTNV    | VARCHAR                               | Chức danh chuyên môn kỹ thuật nghiệp vụ |
| 12  | Nam_PhongChucDanh | YEAR                                  | Năm phong chức danh         |
| 13  | ChuyenNganh       | VARCHAR                               | Chuyên ngành                |
| 14  | DienThoai         | VARCHAR                               | Số điện thoại               |
| 15  | Email             | VARCHAR                               | Email                       |
| 16  | DiaChi            | VARCHAR                               | Địa chỉ                     |
| 17  | PhongBan          | VARCHAR                               | Tên phòng ban chuyên môn    |
| 18  | File_LyLich       | BLOB                                  | File lý lịch khoa học       |

---

## 5. Bảng: TaiKhoan (Tài Khoản)

**Mục đích**: Lưu trữ thông tin tài khoản.

| STT | Tên Trường    | Kiểu Dữ Liệu/Thuộc Tính                | Ghi Chú                     |
|-----|---------------|----------------------------------------|-----------------------------|
| 1   | MaTaiKhoan    | INT, AUTO_INCREMENT, Khóa chính        | Mã tài khoản                |
| 2   | TenDangNhap   | VARCHAR                               | Tên tài khoản đăng nhập     |
| 3   | MatKhau       | VARCHAR(255)                          | Mật khẩu                    |
| 4   | VaiTro        | ENUM('Admin', 'User')                 | Vai trò, mặc định chọn: Admin hoặc User |

**Liên kết**: Bảng `TaiKhoan` liên kết với bảng `CanBo` thông qua `MaCanBo`.

---

## 6. Bảng: VaiTroThamGia (Vai Trò Tham Gia Thực Hiện Đề Tài)

**Mục đích**: Thể hiện vai trò của cán bộ trong thực hiện đề tài.

| STT | Tên Trường    | Kiểu Dữ Liệu/Thuộc Tính                | Ghi Chú                     |
|-----|---------------|----------------------------------------|-----------------------------|
| 1   | MaDeTai       | INT, Khóa ngoại (tham chiếu bảng DeTai) |                             |
| 2   | MaCanBo       | INT, Khóa ngoại (tham chiếu bảng CanBo) |                             |
| 3   | VaiTro        | ENUM('Chủ nhiệm', 'Tham gia')         | Vai trò của cán bộ tham gia đề tài |

**Khóa chính**: Kết hợp `MaDeTai` + `MaCanBo`.

---

## 7. Bảng: ChiTietSanPham_DangI/II/III (Chi Tiết Sản Phẩm)

**Mục đích**: Lưu trữ thông tin chi tiết cho từng loại sản phẩm.

### a. ChiTietSanPham_DangI (Sản Phẩm Dạng I - Vật Chất Hữu Hình)

| STT | Tên Trường        | Kiểu Dữ Liệu/Thuộc Tính                | Ghi Chú                     |
|-----|-------------------|----------------------------------------|-----------------------------|
| 1   | MaSanPham_I       | INT, AUTO_INCREMENT, Khóa chính        | Mã sản phẩm dạng I          |
| 2   | MaDeTai           | INT, Khóa ngoại (tham chiếu bảng DeTai) | Mã đề tài                   |
| 3   | MaDonViHC         | INT, Khóa ngoại (tham chiếu bảng DonViHanhChinh) | Mã đơn vị hành chính |
| 4   | TenSanPham_I      | TEXT                                   | Tên sản phẩm dạng I         |
| 5   | file_SanPham_I    | BLOB                                  | File đính kèm của sản phẩm dạng I |

#### Bảng: DacTinhKyThuat (Đặc Tính Kỹ Thuật của Sản Phẩm Dạng I)

**Mục đích**: Lưu trữ thông tin đặc tính kỹ thuật của sản phẩm dạng I.

| STT | Tên Trường        | Kiểu Dữ Liệu/Thuộc Tính                | Ghi Chú                     |
|-----|-------------------|----------------------------------------|-----------------------------|
| 1   | MaDacTinhKyThuat  | INT, AUTO_INCREMENT, Khóa chính        | Mã đặc tính kỹ thuật        |
| 2   | MaSanPham_I       | INT, Khóa ngoại (tham chiếu bảng ChiTietSanPham_DangI) | Mã sản phẩm dạng I |
| 3   | ThongSo           | VARCHAR                               | Tên thông số kỹ thuật       |
| 4   | DonViDo           | VARCHAR                               | Đơn vị đo                   |
| 5   | GiaTri            | NUMERIC(10,6)                         | Giá trị                     |
| 6   | ChiChu            | TEXT                                  | Ghi chú về đặc tính kỹ thuật |

### b. ChiTietSanPham_DangII (Sản Phẩm Dạng II - Báo Cáo, Quy Trình, Bản Vẽ, Bản Đồ)

| STT | Tên Trường        | Kiểu Dữ Liệu/Thuộc Tính                | Ghi Chú                     |
|-----|-------------------|----------------------------------------|-----------------------------|
| 1   | MaSanPham_II      | INT, AUTO_INCREMENT, Khóa chính        | Mã sản phẩm dạng II         |
| 2   | MaDeTai           | INT, Khóa ngoại (tham chiếu bảng DeTai) | Mã đề tài                   |
| 3   | TenSanPham_II     | TEXT                                   | Tên sản phẩm dạng II        |
| 4   | LoaiSanPham_II    | ENUM('Báo cáo', 'Quy trình', 'Bản vẽ', 'Bản đồ', 'Khác') | Phân loại sản phẩm dạng II, mặc định chọn: Báo cáo, Quy trình, Bản vẽ, Bản đồ, Khác |
| 5   | file_SanPham_II   | BLOB                                  | File đính kèm của sản phẩm dạng II |

### c. ChiTietSanPham_DangIII (Sản Phẩm Dạng III - Công Bố Khoa Học)

| STT | Tên Trường        | Kiểu Dữ Liệu/Thuộc Tính                | Ghi Chú                     |
|-----|-------------------|----------------------------------------|-----------------------------|
| 1   | MaSanPham_III     | INT, AUTO_INCREMENT, Khóa chính        | Mã sản phẩm dạng III        |
| 2   | MaDeTai           | INT, Khóa ngoại (tham chiếu bảng DeTai) | Mã đề tài                   |
| 3   | TenSanPham_III    | TEXT                                   | Tên sản phẩm dạng III       |
| 4   | LoaiSanPham_III   | ENUM('Bằng sáng chế', 'Giải pháp hữu ích', 'Bài báo') | Phân loại sản phẩm dạng III, mặc định chọn: Bằng sáng chế, Giải pháp hữu ích, Bài báo |
| 5   | NoiCongBo         | VARCHAR                               | Nơi công bố sản phẩm        |
| 6   | file_SanPham_III  | BLOB                                  | File đính kèm của sản phẩm dạng III |

---

## 8. Bảng: KinhPhi (Kinh Phí)

**Mục đích**: Quản lý kinh phí của đề tài.

| STT | Tên Trường    | Kiểu Dữ Liệu/Thuộc Tính                | Ghi Chú                     |
|-----|---------------|----------------------------------------|-----------------------------|
| 1   | MaKinhPhi     | INT, AUTO_INCREMENT, Khóa chính        | Mã kinh phí                 |
| 2   | MaDeTai       | INT, Khóa ngoại (tham chiếu bảng DeTai) | Mã đề tài                   |
| 3   | NganSach      | BIGINT                                | Số tiền từ ngân sách        |
| 4   | Khac          | BIGINT                                | Số tiền từ các nguồn khác   |

---

## 🔗 Các Mối Quan Hệ Chính

- **Một đề tài - Nhiều sản phẩm**: Một đề tài (`DeTai`) có thể có nhiều sản phẩm dạng I, II, III (quan hệ 1-n thông qua các bảng `ChiTietSanPham_DangI`, `ChiTietSanPham_DangII`, `ChiTietSanPham_DangIII`).
- **Một đề tài - Nhiều cán bộ**: Một đề tài (`DeTai`) có nhiều cán bộ tham gia (quan hệ n-n thông qua bảng `VaiTroThamGia`) với vai trò khác nhau (Chủ nhiệm/Tham gia).
- **Một đề tài - Nhiều đơn vị**: Một đề tài (`DeTai`) liên quan đến nhiều đơn vị (quan hệ n-n thông qua bảng `DeTai_DonVi`) với vai trò phối hợp thực hiện.
- **Một sản phẩm dạng I - Một đơn vị hành chính**: Một sản phẩm dạng I (`ChiTietSanPham_DangI`) được gắn với một đơn vị hành chính (`DonViHanhChinh`) (quan hệ 1-n). Ví dụ: Hệ thống lọc nước nhiễm mặn thuộc tỉnh Bạc Liêu.

---

**Tài liệu này cung cấp cấu trúc cơ sở dữ liệu hoàn chỉnh cho Hệ thống Quản lý Đề tài Nghiên cứu Khoa học.**
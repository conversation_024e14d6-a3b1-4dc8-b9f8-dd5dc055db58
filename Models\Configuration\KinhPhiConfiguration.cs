using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Models.Models;

namespace Models.Configuration
{
    public class KinhPhiConfiguration : IEntityTypeConfiguration<KinhPhi>
    {
        public void Configure(EntityTypeBuilder<KinhPhi> builder)
        {
            builder.ToTable("KinhPhi");

            builder.<PERSON><PERSON><PERSON>(e => e.<PERSON><PERSON>);

            builder.Property(e => e.<PERSON>)
                .ValueGeneratedOnAdd()
                .HasColumnName("MaKinhPhi");

            builder.Property(e => e.MaDeTai)
                .HasColumnName("MaDeTai");

            builder.Property(e => e.<PERSON>an<PERSON>ach)
                .HasColumnType("BIGINT")
                .HasColumnName("NganSach");

            builder.Property(e => e.Khac)
                .HasColumnType("BIGINT")
                .HasColumnName("Khac");

            // Configure relationships
            builder.HasOne(e => e.De<PERSON>ai)
                .WithMany(e => e.<PERSON><PERSON><PERSON><PERSON>)
                .HasForeignKey(e => e.<PERSON><PERSON>e<PERSON><PERSON>)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}

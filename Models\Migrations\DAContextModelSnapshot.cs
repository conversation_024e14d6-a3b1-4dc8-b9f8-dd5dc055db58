﻿// <auto-generated />
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Models.HandleData;
using System;

#nullable disable

namespace Models.Migrations
{
    [DbContext(typeof(DAContext))]
    partial class DAContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.16")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Models.Models.CanBo", b =>
                {
                    b.Property<int>("MaCanBo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("MaCanBo");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaCanBo"));

                    b.Property<string>("ChucDanhCMKTNV")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("ChucDanhCMKTNV");

                    b.Property<string>("ChucVu")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("ChucVu");

                    b.Property<string>("ChuyenNganh")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("ChuyenNganh");

                    b.Property<string>("DiaChi")
                        .HasColumnType("VARCHAR(500)")
                        .HasColumnName("DiaChi");

                    b.Property<string>("DienThoai")
                        .HasColumnType("VARCHAR(20)")
                        .HasColumnName("DienThoai");

                    b.Property<string>("Email")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("Email");

                    b.Property<byte[]>("File_LyLich")
                        .HasColumnType("VARBINARY(MAX)")
                        .HasColumnName("File_LyLich");

                    b.Property<string>("GioiTinh")
                        .IsRequired()
                        .HasColumnType("VARCHAR(10)")
                        .HasColumnName("GioiTinh");

                    b.Property<string>("HoTen")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("HoTen");

                    b.Property<string>("HocHam")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("HocHam");

                    b.Property<string>("HocVi")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("HocVi");

                    b.Property<int?>("Nam_HocHam")
                        .HasColumnType("INT")
                        .HasColumnName("Nam_HocHam");

                    b.Property<int?>("Nam_HocVi")
                        .HasColumnType("INT")
                        .HasColumnName("Nam_HocVi");

                    b.Property<int?>("Nam_PhongChucDanh")
                        .HasColumnType("INT")
                        .HasColumnName("Nam_PhongChucDanh");

                    b.Property<DateTime?>("NgaySinh")
                        .HasColumnType("DATE")
                        .HasColumnName("NgaySinh");

                    b.Property<string>("PhongBan")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("PhongBan");

                    b.Property<string>("QuanHam")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("QuanHam");

                    b.HasKey("MaCanBo");

                    b.ToTable("CanBo", (string)null);
                });

            modelBuilder.Entity("Models.Models.ChiTietSanPham_DangI", b =>
                {
                    b.Property<int>("MaSanPham_I")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("MaSanPham_I");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaSanPham_I"));

                    b.Property<int>("MaDeTai")
                        .HasColumnType("int")
                        .HasColumnName("MaDeTai");

                    b.Property<int>("MaDonViHC")
                        .HasColumnType("int")
                        .HasColumnName("MaDonViHC");

                    b.Property<string>("TenSanPham_I")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("TenSanPham_I");

                    b.Property<byte[]>("file_SanPham_I")
                        .HasColumnType("VARBINARY(MAX)")
                        .HasColumnName("file_SanPham_I");

                    b.HasKey("MaSanPham_I");

                    b.HasIndex("MaDeTai");

                    b.HasIndex("MaDonViHC");

                    b.ToTable("ChiTietSanPham_DangI", (string)null);
                });

            modelBuilder.Entity("Models.Models.ChiTietSanPham_DangII", b =>
                {
                    b.Property<int>("MaSanPham_II")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("MaSanPham_II");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaSanPham_II"));

                    b.Property<string>("LoaiSanPham_II")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("VARCHAR(50)")
                        .HasDefaultValue("BaoCao")
                        .HasColumnName("LoaiSanPham_II");

                    b.Property<int>("MaDeTai")
                        .HasColumnType("int")
                        .HasColumnName("MaDeTai");

                    b.Property<string>("TenSanPham_II")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("TenSanPham_II");

                    b.Property<byte[]>("file_SanPham_II")
                        .HasColumnType("VARBINARY(MAX)")
                        .HasColumnName("file_SanPham_II");

                    b.HasKey("MaSanPham_II");

                    b.HasIndex("MaDeTai");

                    b.ToTable("ChiTietSanPham_DangII", (string)null);
                });

            modelBuilder.Entity("Models.Models.ChiTietSanPham_DangIII", b =>
                {
                    b.Property<int>("MaSanPham_III")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("MaSanPham_III");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaSanPham_III"));

                    b.Property<string>("LoaiSanPham_III")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("VARCHAR(50)")
                        .HasDefaultValue("BangSangChe")
                        .HasColumnName("LoaiSanPham_III");

                    b.Property<int>("MaDeTai")
                        .HasColumnType("int")
                        .HasColumnName("MaDeTai");

                    b.Property<string>("NoiCongBo")
                        .HasColumnType("VARCHAR(500)")
                        .HasColumnName("NoiCongBo");

                    b.Property<string>("TenSanPham_III")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("TenSanPham_III");

                    b.Property<byte[]>("file_SanPham_III")
                        .HasColumnType("VARBINARY(MAX)")
                        .HasColumnName("file_SanPham_III");

                    b.HasKey("MaSanPham_III");

                    b.HasIndex("MaDeTai");

                    b.ToTable("ChiTietSanPham_DangIII", (string)null);
                });

            modelBuilder.Entity("Models.Models.DacTinhKyThuat", b =>
                {
                    b.Property<int>("MaDacTinhKyThuat")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("MaDacTinhKyThuat");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaDacTinhKyThuat"));

                    b.Property<string>("ChiChu")
                        .HasColumnType("TEXT")
                        .HasColumnName("ChiChu");

                    b.Property<string>("DonViDo")
                        .HasColumnType("VARCHAR(50)")
                        .HasColumnName("DonViDo");

                    b.Property<decimal?>("GiaTri")
                        .HasColumnType("NUMERIC(10,6)")
                        .HasColumnName("GiaTri");

                    b.Property<int>("MaSanPham_I")
                        .HasColumnType("int")
                        .HasColumnName("MaSanPham_I");

                    b.Property<string>("ThongSo")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("ThongSo");

                    b.HasKey("MaDacTinhKyThuat");

                    b.HasIndex("MaSanPham_I");

                    b.ToTable("DacTinhKyThuat", (string)null);
                });

            modelBuilder.Entity("Models.Models.DeTai", b =>
                {
                    b.Property<int>("MaDeTai")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("MaDeTai");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaDeTai"));

                    b.Property<string>("CapQuanLy")
                        .IsRequired()
                        .HasColumnType("VARCHAR(50)")
                        .HasColumnName("CapQuanLy");

                    b.Property<string>("LinhVuc")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("LinhVuc");

                    b.Property<string>("MoTaTomTat")
                        .HasColumnType("TEXT")
                        .HasColumnName("MoTaTomTat");

                    b.Property<string>("TenDeTai")
                        .HasColumnType("TEXT")
                        .HasColumnName("TenDeTai");

                    b.Property<DateTime?>("ThoiGianBatDau")
                        .HasColumnType("DATE")
                        .HasColumnName("ThoiGianBatDau");

                    b.Property<DateTime?>("ThoiGianKetThuc")
                        .HasColumnType("DATE")
                        .HasColumnName("ThoiGianKetThuc");

                    b.HasKey("MaDeTai");

                    b.ToTable("DeTai", (string)null);
                });

            modelBuilder.Entity("Models.Models.DeTai_DonVi", b =>
                {
                    b.Property<int>("MaDeTai")
                        .HasColumnType("int")
                        .HasColumnName("MaDeTai");

                    b.Property<int>("MaDonVi")
                        .HasColumnType("int")
                        .HasColumnName("MaDonVi");

                    b.Property<string>("GhiChu")
                        .HasColumnType("TEXT")
                        .HasColumnName("GhiChu");

                    b.HasKey("MaDeTai", "MaDonVi");

                    b.HasIndex("MaDonVi");

                    b.ToTable("DeTai_DonVi", (string)null);
                });

            modelBuilder.Entity("Models.Models.DonViHanhChinh", b =>
                {
                    b.Property<int>("MaDonViHC")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("MaDonViHC");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaDonViHC"));

                    b.Property<string>("TinhThanh")
                        .IsRequired()
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("TinhThanh");

                    b.HasKey("MaDonViHC");

                    b.ToTable("DonViHanhChinh", (string)null);
                });

            modelBuilder.Entity("Models.Models.DonViPhoiHop", b =>
                {
                    b.Property<int>("MaDonVi")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("MaDonVi");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaDonVi"));

                    b.Property<string>("DiaChi")
                        .HasColumnType("VARCHAR(500)")
                        .HasColumnName("DiaChi");

                    b.Property<string>("Email")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("Email");

                    b.Property<string>("SoDienThoai")
                        .HasColumnType("VARCHAR(20)")
                        .HasColumnName("SoDienThoai");

                    b.Property<string>("TenDonVi")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("TenDonVi");

                    b.HasKey("MaDonVi");

                    b.ToTable("DonViPhoiHop", (string)null);
                });

            modelBuilder.Entity("Models.Models.KinhPhi", b =>
                {
                    b.Property<int>("MaKinhPhi")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("MaKinhPhi");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaKinhPhi"));

                    b.Property<long?>("Khac")
                        .HasColumnType("BIGINT")
                        .HasColumnName("Khac");

                    b.Property<int>("MaDeTai")
                        .HasColumnType("int")
                        .HasColumnName("MaDeTai");

                    b.Property<long?>("NganSach")
                        .HasColumnType("BIGINT")
                        .HasColumnName("NganSach");

                    b.HasKey("MaKinhPhi");

                    b.HasIndex("MaDeTai");

                    b.ToTable("KinhPhi", (string)null);
                });

            modelBuilder.Entity("Models.Models.TaiKhoan", b =>
                {
                    b.Property<int>("MaTaiKhoan")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("MaTaiKhoan");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaTaiKhoan"));

                    b.Property<int>("MaCanBo")
                        .HasColumnType("int")
                        .HasColumnName("MaCanBo");

                    b.Property<string>("MatKhau")
                        .IsRequired()
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("MatKhau");

                    b.Property<string>("TenDangNhap")
                        .IsRequired()
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("TenDangNhap");

                    b.Property<string>("VaiTro")
                        .IsRequired()
                        .HasColumnType("VARCHAR(20)")
                        .HasColumnName("VaiTro");

                    b.HasKey("MaTaiKhoan");

                    b.HasIndex("MaCanBo")
                        .IsUnique();

                    b.HasIndex("TenDangNhap")
                        .IsUnique();

                    b.ToTable("TaiKhoan", (string)null);
                });

            modelBuilder.Entity("Models.Models.VaiTroThamGia", b =>
                {
                    b.Property<int>("MaDeTai")
                        .HasColumnType("int")
                        .HasColumnName("MaDeTai");

                    b.Property<int>("MaCanBo")
                        .HasColumnType("int")
                        .HasColumnName("MaCanBo");

                    b.Property<string>("VaiTro")
                        .IsRequired()
                        .HasColumnType("VARCHAR(50)")
                        .HasColumnName("VaiTro");

                    b.HasKey("MaDeTai", "MaCanBo");

                    b.HasIndex("MaCanBo");

                    b.ToTable("VaiTroThamGia", (string)null);
                });

            modelBuilder.Entity("Models.Models.ChiTietSanPham_DangI", b =>
                {
                    b.HasOne("Models.Models.DeTai", "DeTai")
                        .WithMany("ChiTietSanPham_DangI")
                        .HasForeignKey("MaDeTai")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Models.Models.DonViHanhChinh", "DonViHanhChinh")
                        .WithMany("ChiTietSanPham_DangI")
                        .HasForeignKey("MaDonViHC")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("DeTai");

                    b.Navigation("DonViHanhChinh");
                });

            modelBuilder.Entity("Models.Models.ChiTietSanPham_DangII", b =>
                {
                    b.HasOne("Models.Models.DeTai", "DeTai")
                        .WithMany("ChiTietSanPham_DangII")
                        .HasForeignKey("MaDeTai")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DeTai");
                });

            modelBuilder.Entity("Models.Models.ChiTietSanPham_DangIII", b =>
                {
                    b.HasOne("Models.Models.DeTai", "DeTai")
                        .WithMany("ChiTietSanPham_DangIII")
                        .HasForeignKey("MaDeTai")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DeTai");
                });

            modelBuilder.Entity("Models.Models.DacTinhKyThuat", b =>
                {
                    b.HasOne("Models.Models.ChiTietSanPham_DangI", "ChiTietSanPham_DangI")
                        .WithMany("DacTinhKyThuat")
                        .HasForeignKey("MaSanPham_I")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ChiTietSanPham_DangI");
                });

            modelBuilder.Entity("Models.Models.DeTai_DonVi", b =>
                {
                    b.HasOne("Models.Models.DeTai", "DeTai")
                        .WithMany("DeTai_DonVi")
                        .HasForeignKey("MaDeTai")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Models.Models.DonViPhoiHop", "DonViPhoiHop")
                        .WithMany("DeTai_DonVi")
                        .HasForeignKey("MaDonVi")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DeTai");

                    b.Navigation("DonViPhoiHop");
                });

            modelBuilder.Entity("Models.Models.KinhPhi", b =>
                {
                    b.HasOne("Models.Models.DeTai", "DeTai")
                        .WithMany("KinhPhi")
                        .HasForeignKey("MaDeTai")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DeTai");
                });

            modelBuilder.Entity("Models.Models.TaiKhoan", b =>
                {
                    b.HasOne("Models.Models.CanBo", "CanBo")
                        .WithOne("TaiKhoan")
                        .HasForeignKey("Models.Models.TaiKhoan", "MaCanBo")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CanBo");
                });

            modelBuilder.Entity("Models.Models.VaiTroThamGia", b =>
                {
                    b.HasOne("Models.Models.CanBo", "CanBo")
                        .WithMany("VaiTroThamGia")
                        .HasForeignKey("MaCanBo")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Models.Models.DeTai", "DeTai")
                        .WithMany("VaiTroThamGia")
                        .HasForeignKey("MaDeTai")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CanBo");

                    b.Navigation("DeTai");
                });

            modelBuilder.Entity("Models.Models.CanBo", b =>
                {
                    b.Navigation("TaiKhoan");

                    b.Navigation("VaiTroThamGia");
                });

            modelBuilder.Entity("Models.Models.ChiTietSanPham_DangI", b =>
                {
                    b.Navigation("DacTinhKyThuat");
                });

            modelBuilder.Entity("Models.Models.DeTai", b =>
                {
                    b.Navigation("ChiTietSanPham_DangI");

                    b.Navigation("ChiTietSanPham_DangII");

                    b.Navigation("ChiTietSanPham_DangIII");

                    b.Navigation("DeTai_DonVi");

                    b.Navigation("KinhPhi");

                    b.Navigation("VaiTroThamGia");
                });

            modelBuilder.Entity("Models.Models.DonViHanhChinh", b =>
                {
                    b.Navigation("ChiTietSanPham_DangI");
                });

            modelBuilder.Entity("Models.Models.DonViPhoiHop", b =>
                {
                    b.Navigation("DeTai_DonVi");
                });
#pragma warning restore 612, 618
        }
    }
}

﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Models;
using Models.HandleData;

#nullable disable

namespace Models.Migrations
{
    [DbContext(typeof(DAContext))]
    [Migration("20250523165616_Initialize")]
    partial class Initialize
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.16")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Models.Models.CanBo", b =>
                {
                    b.Property<int>("MaCanBo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("MaCanBo");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaCanBo"));

                    b.Property<string>("ChucDanhCMKTNV")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("ChucDanhCMKTNV");

                    b.Property<string>("ChucVu")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("ChucVu");

                    b.Property<string>("ChuyenNganh")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("ChuyenNganh");

                    b.Property<string>("DiaChi")
                        .HasColumnType("VARCHAR(500)")
                        .HasColumnName("DiaChi");

                    b.Property<string>("DienThoai")
                        .HasColumnType("VARCHAR(20)")
                        .HasColumnName("DienThoai");

                    b.Property<string>("Email")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("Email");

                    b.Property<byte[]>("File_LyLich")
                        .HasColumnType("VARBINARY(MAX)")
                        .HasColumnName("File_LyLich");

                    b.Property<string>("GioiTinh")
                        .IsRequired()
                        .HasColumnType("VARCHAR(10)")
                        .HasColumnName("GioiTinh");

                    b.Property<string>("HoTen")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("HoTen");

                    b.Property<string>("HocHam")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("HocHam");

                    b.Property<string>("HocVi")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("HocVi");

                    b.Property<int?>("Nam_HocHam")
                        .HasColumnType("INT")
                        .HasColumnName("Nam_HocHam");

                    b.Property<int?>("Nam_HocVi")
                        .HasColumnType("INT")
                        .HasColumnName("Nam_HocVi");

                    b.Property<int?>("Nam_PhongChucDanh")
                        .HasColumnType("INT")
                        .HasColumnName("Nam_PhongChucDanh");

                    b.Property<DateTime?>("NgaySinh")
                        .HasColumnType("DATE")
                        .HasColumnName("NgaySinh");

                    b.Property<string>("PhongBan")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("PhongBan");

                    b.Property<string>("QuanHam")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("QuanHam");

                    b.HasKey("MaCanBo");

                    b.ToTable("CanBo", (string)null);
                });

            modelBuilder.Entity("Models.Models.ChiTietSanPham_DangI", b =>
                {
                    b.Property<int>("MaSanPham")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("MaSanPham");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaSanPham"));

                    b.Property<string>("DonViTinh")
                        .HasColumnType("VARCHAR(50)")
                        .HasColumnName("DonViTinh");

                    b.Property<decimal?>("GiaTri")
                        .HasColumnType("DECIMAL(18,2)")
                        .HasColumnName("GiaTri");

                    b.Property<int>("MaDeTai")
                        .HasColumnType("int")
                        .HasColumnName("MaDeTai");

                    b.Property<int?>("MaDonViHC")
                        .HasColumnType("int")
                        .HasColumnName("MaDonViHC");

                    b.Property<string>("MoTa")
                        .HasColumnType("TEXT")
                        .HasColumnName("MoTa");

                    b.Property<DateTime?>("NgayHoanThanh")
                        .HasColumnType("DATE")
                        .HasColumnName("NgayHoanThanh");

                    b.Property<int?>("SoLuong")
                        .HasColumnType("INT")
                        .HasColumnName("SoLuong");

                    b.Property<string>("TenSanPham")
                        .HasColumnType("VARCHAR(500)")
                        .HasColumnName("TenSanPham");

                    b.Property<string>("TrangThai")
                        .HasColumnType("VARCHAR(100)")
                        .HasColumnName("TrangThai");

                    b.HasKey("MaSanPham");

                    b.HasIndex("MaDeTai");

                    b.HasIndex("MaDonViHC");

                    b.ToTable("ChiTietSanPham_DangI", (string)null);
                });

            modelBuilder.Entity("Models.Models.ChiTietSanPham_DangII", b =>
                {
                    b.Property<int>("MaBaoCao")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("MaBaoCao");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaBaoCao"));

                    b.Property<byte[]>("FileBaoCao")
                        .HasColumnType("VARBINARY(MAX)")
                        .HasColumnName("FileBaoCao");

                    b.Property<string>("LoaiBaoCao")
                        .HasColumnType("VARCHAR(100)")
                        .HasColumnName("LoaiBaoCao");

                    b.Property<int>("MaDeTai")
                        .HasColumnType("int")
                        .HasColumnName("MaDeTai");

                    b.Property<string>("MoTa")
                        .HasColumnType("TEXT")
                        .HasColumnName("MoTa");

                    b.Property<DateTime?>("NgayHoanThanh")
                        .HasColumnType("DATE")
                        .HasColumnName("NgayHoanThanh");

                    b.Property<string>("TenBaoCao")
                        .HasColumnType("VARCHAR(500)")
                        .HasColumnName("TenBaoCao");

                    b.Property<string>("TomTatNoiDung")
                        .HasColumnType("TEXT")
                        .HasColumnName("TomTatNoiDung");

                    b.Property<string>("TrangThai")
                        .HasColumnType("VARCHAR(100)")
                        .HasColumnName("TrangThai");

                    b.HasKey("MaBaoCao");

                    b.HasIndex("MaDeTai");

                    b.ToTable("ChiTietSanPham_DangII", (string)null);
                });

            modelBuilder.Entity("Models.Models.ChiTietSanPham_DangIII", b =>
                {
                    b.Property<int>("MaBaiBao")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("MaBaiBao");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaBaiBao"));

                    b.Property<string>("DOI")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("DOI");

                    b.Property<string>("ISSN")
                        .HasColumnType("VARCHAR(20)")
                        .HasColumnName("ISSN");

                    b.Property<string>("LoaiBaiBao")
                        .HasColumnType("VARCHAR(100)")
                        .HasColumnName("LoaiBaiBao");

                    b.Property<int>("MaDeTai")
                        .HasColumnType("int")
                        .HasColumnName("MaDeTai");

                    b.Property<int?>("Nam")
                        .HasColumnType("INT")
                        .HasColumnName("Nam");

                    b.Property<DateTime?>("NgayXuatBan")
                        .HasColumnType("DATE")
                        .HasColumnName("NgayXuatBan");

                    b.Property<int?>("So")
                        .HasColumnType("INT")
                        .HasColumnName("So");

                    b.Property<string>("TacGia")
                        .HasColumnType("VARCHAR(500)")
                        .HasColumnName("TacGia");

                    b.Property<string>("TenTapChi")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("TenTapChi");

                    b.Property<string>("TieuDe")
                        .HasColumnType("VARCHAR(500)")
                        .HasColumnName("TieuDe");

                    b.Property<string>("TomTat")
                        .HasColumnType("TEXT")
                        .HasColumnName("TomTat");

                    b.Property<string>("Trang")
                        .HasColumnType("VARCHAR(50)")
                        .HasColumnName("Trang");

                    b.Property<string>("TrangThai")
                        .HasColumnType("VARCHAR(100)")
                        .HasColumnName("TrangThai");

                    b.HasKey("MaBaiBao");

                    b.HasIndex("MaDeTai");

                    b.ToTable("ChiTietSanPham_DangIII", (string)null);
                });

            modelBuilder.Entity("Models.Models.DacTinhKyThuat", b =>
                {
                    b.Property<int>("MaDacTinh")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("MaDacTinh");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaDacTinh"));

                    b.Property<string>("DonVi")
                        .HasColumnType("VARCHAR(50)")
                        .HasColumnName("DonVi");

                    b.Property<string>("GiaTri")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("GiaTri");

                    b.Property<int>("MaSanPham")
                        .HasColumnType("int")
                        .HasColumnName("MaSanPham");

                    b.Property<string>("MoTa")
                        .HasColumnType("TEXT")
                        .HasColumnName("MoTa");

                    b.Property<string>("TenDacTinh")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("TenDacTinh");

                    b.HasKey("MaDacTinh");

                    b.HasIndex("MaSanPham");

                    b.ToTable("DacTinhKyThuat", (string)null);
                });

            modelBuilder.Entity("Models.Models.DeTai", b =>
                {
                    b.Property<int>("MaDeTai")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("MaDeTai");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaDeTai"));

                    b.Property<string>("CapQuanLy")
                        .IsRequired()
                        .HasColumnType("VARCHAR(50)")
                        .HasColumnName("CapQuanLy");

                    b.Property<string>("LinhVuc")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("LinhVuc");

                    b.Property<string>("MoTaTomTat")
                        .HasColumnType("TEXT")
                        .HasColumnName("MoTaTomTat");

                    b.Property<string>("TenDeTai")
                        .HasColumnType("TEXT")
                        .HasColumnName("TenDeTai");

                    b.Property<DateTime?>("ThoiGianBatDau")
                        .HasColumnType("DATE")
                        .HasColumnName("ThoiGianBatDau");

                    b.Property<DateTime?>("ThoiGianKetThuc")
                        .HasColumnType("DATE")
                        .HasColumnName("ThoiGianKetThuc");

                    b.HasKey("MaDeTai");

                    b.ToTable("DeTai", (string)null);
                });

            modelBuilder.Entity("Models.Models.DeTai_DonVi", b =>
                {
                    b.Property<int>("MaDeTai")
                        .HasColumnType("int")
                        .HasColumnName("MaDeTai");

                    b.Property<int>("MaDonVi")
                        .HasColumnType("int")
                        .HasColumnName("MaDonVi");

                    b.Property<string>("GhiChu")
                        .HasColumnType("TEXT")
                        .HasColumnName("GhiChu");

                    b.HasKey("MaDeTai", "MaDonVi");

                    b.HasIndex("MaDonVi");

                    b.ToTable("DeTai_DonVi", (string)null);
                });

            modelBuilder.Entity("Models.Models.DonViHanhChinh", b =>
                {
                    b.Property<int>("MaDonViHC")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("MaDonViHC");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaDonViHC"));

                    b.Property<string>("TinhThanh")
                        .IsRequired()
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("TinhThanh");

                    b.HasKey("MaDonViHC");

                    b.ToTable("DonViHanhChinh", (string)null);
                });

            modelBuilder.Entity("Models.Models.DonViPhoiHop", b =>
                {
                    b.Property<int>("MaDonVi")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("MaDonVi");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaDonVi"));

                    b.Property<string>("DiaChi")
                        .HasColumnType("VARCHAR(500)")
                        .HasColumnName("DiaChi");

                    b.Property<string>("Email")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("Email");

                    b.Property<string>("SoDienThoai")
                        .HasColumnType("VARCHAR(20)")
                        .HasColumnName("SoDienThoai");

                    b.Property<string>("TenDonVi")
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("TenDonVi");

                    b.HasKey("MaDonVi");

                    b.ToTable("DonViPhoiHop", (string)null);
                });

            modelBuilder.Entity("Models.Models.KinhPhi", b =>
                {
                    b.Property<int>("MaKinhPhi")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("MaKinhPhi");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaKinhPhi"));

                    b.Property<long?>("Khac")
                        .HasColumnType("BIGINT")
                        .HasColumnName("Khac");

                    b.Property<int>("MaDeTai")
                        .HasColumnType("int")
                        .HasColumnName("MaDeTai");

                    b.Property<long?>("NganSach")
                        .HasColumnType("BIGINT")
                        .HasColumnName("NganSach");

                    b.HasKey("MaKinhPhi");

                    b.HasIndex("MaDeTai");

                    b.ToTable("KinhPhi", (string)null);
                });

            modelBuilder.Entity("Models.Models.TaiKhoan", b =>
                {
                    b.Property<int>("MaTaiKhoan")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("MaTaiKhoan");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MaTaiKhoan"));

                    b.Property<int>("MaCanBo")
                        .HasColumnType("int")
                        .HasColumnName("MaCanBo");

                    b.Property<string>("MatKhau")
                        .IsRequired()
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("MatKhau");

                    b.Property<string>("TenDangNhap")
                        .IsRequired()
                        .HasColumnType("VARCHAR(255)")
                        .HasColumnName("TenDangNhap");

                    b.Property<string>("VaiTro")
                        .IsRequired()
                        .HasColumnType("VARCHAR(20)")
                        .HasColumnName("VaiTro");

                    b.HasKey("MaTaiKhoan");

                    b.HasIndex("MaCanBo")
                        .IsUnique();

                    b.HasIndex("TenDangNhap")
                        .IsUnique();

                    b.ToTable("TaiKhoan", (string)null);
                });

            modelBuilder.Entity("Models.Models.VaiTroThamGia", b =>
                {
                    b.Property<int>("MaDeTai")
                        .HasColumnType("int")
                        .HasColumnName("MaDeTai");

                    b.Property<int>("MaCanBo")
                        .HasColumnType("int")
                        .HasColumnName("MaCanBo");

                    b.Property<string>("VaiTro")
                        .IsRequired()
                        .HasColumnType("VARCHAR(50)")
                        .HasColumnName("VaiTro");

                    b.HasKey("MaDeTai", "MaCanBo");

                    b.HasIndex("MaCanBo");

                    b.ToTable("VaiTroThamGia", (string)null);
                });

            modelBuilder.Entity("Models.Models.ChiTietSanPham_DangI", b =>
                {
                    b.HasOne("Models.Models.DeTai", "DeTai")
                        .WithMany("ChiTietSanPham_DangI")
                        .HasForeignKey("MaDeTai")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Models.Models.DonViHanhChinh", "DonViHanhChinh")
                        .WithMany("ChiTietSanPham_DangI")
                        .HasForeignKey("MaDonViHC")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("DeTai");

                    b.Navigation("DonViHanhChinh");
                });

            modelBuilder.Entity("Models.Models.ChiTietSanPham_DangII", b =>
                {
                    b.HasOne("Models.Models.DeTai", "DeTai")
                        .WithMany("ChiTietSanPham_DangII")
                        .HasForeignKey("MaDeTai")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DeTai");
                });

            modelBuilder.Entity("Models.Models.ChiTietSanPham_DangIII", b =>
                {
                    b.HasOne("Models.Models.DeTai", "DeTai")
                        .WithMany("ChiTietSanPham_DangIII")
                        .HasForeignKey("MaDeTai")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DeTai");
                });

            modelBuilder.Entity("Models.Models.DacTinhKyThuat", b =>
                {
                    b.HasOne("Models.Models.ChiTietSanPham_DangI", "ChiTietSanPham_DangI")
                        .WithMany("DacTinhKyThuat")
                        .HasForeignKey("MaSanPham")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ChiTietSanPham_DangI");
                });

            modelBuilder.Entity("Models.Models.DeTai_DonVi", b =>
                {
                    b.HasOne("Models.Models.DeTai", "DeTai")
                        .WithMany("DeTai_DonVi")
                        .HasForeignKey("MaDeTai")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Models.Models.DonViPhoiHop", "DonViPhoiHop")
                        .WithMany("DeTai_DonVi")
                        .HasForeignKey("MaDonVi")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DeTai");

                    b.Navigation("DonViPhoiHop");
                });

            modelBuilder.Entity("Models.Models.KinhPhi", b =>
                {
                    b.HasOne("Models.Models.DeTai", "DeTai")
                        .WithMany("KinhPhi")
                        .HasForeignKey("MaDeTai")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DeTai");
                });

            modelBuilder.Entity("Models.Models.TaiKhoan", b =>
                {
                    b.HasOne("Models.Models.CanBo", "CanBo")
                        .WithOne("TaiKhoan")
                        .HasForeignKey("Models.Models.TaiKhoan", "MaCanBo")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CanBo");
                });

            modelBuilder.Entity("Models.Models.VaiTroThamGia", b =>
                {
                    b.HasOne("Models.Models.CanBo", "CanBo")
                        .WithMany("VaiTroThamGia")
                        .HasForeignKey("MaCanBo")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Models.Models.DeTai", "DeTai")
                        .WithMany("VaiTroThamGia")
                        .HasForeignKey("MaDeTai")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CanBo");

                    b.Navigation("DeTai");
                });

            modelBuilder.Entity("Models.Models.CanBo", b =>
                {
                    b.Navigation("TaiKhoan");

                    b.Navigation("VaiTroThamGia");
                });

            modelBuilder.Entity("Models.Models.ChiTietSanPham_DangI", b =>
                {
                    b.Navigation("DacTinhKyThuat");
                });

            modelBuilder.Entity("Models.Models.DeTai", b =>
                {
                    b.Navigation("ChiTietSanPham_DangI");

                    b.Navigation("ChiTietSanPham_DangII");

                    b.Navigation("ChiTietSanPham_DangIII");

                    b.Navigation("DeTai_DonVi");

                    b.Navigation("KinhPhi");

                    b.Navigation("VaiTroThamGia");
                });

            modelBuilder.Entity("Models.Models.DonViHanhChinh", b =>
                {
                    b.Navigation("ChiTietSanPham_DangI");
                });

            modelBuilder.Entity("Models.Models.DonViPhoiHop", b =>
                {
                    b.Navigation("DeTai_DonVi");
                });
#pragma warning restore 612, 618
        }
    }
}

using Microsoft.EntityFrameworkCore;
using Models.HandleData;
using Models.Models;

namespace WinFormsApp1
{
    public partial class frmThemThanhVien : Form
    {
        private int deTaiId;

        public int SelectedCanBoId { get; private set; }
        public VaiTroThamGiaEnum SelectedVaiTro { get; private set; }

        public frmThemThanhVien(int deTaiId)
        {
            this.deTaiId = deTaiId;
            InitializeComponent();
            LoadData();
        }



        private async void LoadData()
        {
            try
            {
                using (var context = new DAContext())
                {
                    // Load danh sách cán bộ chưa tham gia đề tài này
                    var existingCanBoIds = await context.VaiTroThamGia
                        .Where(vt => vt.MaDeTai == deTaiId)
                        .Select(vt => vt.MaCanBo)
                        .ToListAsync();

                    var availableCanBo = await context.CanBo
                        .Where(cb => !existingCanBoIds.Contains(cb.MaCanBo))
                        .ToListAsync();

                    cmbCanBo.Items.Clear();
                    foreach (var canBo in availableCanBo)
                    {
                        cmbCanBo.Items.Add(new ComboBoxItem { Text = canBo.HoTen ?? "N/A", Value = canBo.MaCanBo });
                    }

                    if (cmbCanBo.Items.Count > 0)
                        cmbCanBo.SelectedIndex = 0;

                    cmbVaiTro.SelectedIndex = 1; // Mặc định là "Tham gia"
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi khi tải dữ liệu: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnLuu_Click(object sender, EventArgs e)
        {
            if (cmbCanBo.SelectedItem == null)
            {
                MessageBox.Show("Vui lòng chọn cán bộ!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (cmbVaiTro.SelectedIndex < 0)
            {
                MessageBox.Show("Vui lòng chọn vai trò!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedItem = (ComboBoxItem)cmbCanBo.SelectedItem;
            SelectedCanBoId = selectedItem.Value;
            SelectedVaiTro = cmbVaiTro.SelectedIndex == 0 ? VaiTroThamGiaEnum.ChuNhiem : VaiTroThamGiaEnum.ThamGia;

            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void BtnHuy_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }

    public class ComboBoxItem
    {
        public string Text { get; set; } = string.Empty;
        public int Value { get; set; }

        public override string ToString()
        {
            return Text;
        }
    }
}
